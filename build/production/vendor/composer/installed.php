<?php return array(
    'root' => array(
        'name' => 'responsive-sk/hdm-boot',
        'pretty_version' => '2.1.2',
        'version' => '2.1.2.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'brick/math' => array(
            'pretty_version' => '0.13.1',
            'version' => '0.13.1.0',
            'reference' => 'fc7ed316430118cc7836bf45faff18d5dfc8de04',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cakephp/core' => array(
            'pretty_version' => '5.2.5',
            'version' => '5.2.5.0',
            'reference' => 'a0a92ee7fbb7b7555dbf4ea7ff3fd4e779693da6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cakephp/core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cakephp/utility' => array(
            'pretty_version' => '5.2.5',
            'version' => '5.2.5.0',
            'reference' => '7eaef40766bf671332adfacdc2d6fb9ea8aea5de',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cakephp/utility',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cakephp/validation' => array(
            'pretty_version' => '5.2.5',
            'version' => '5.2.5.0',
            'reference' => '230ec404a907aba9d4212f42f1348ee78c55cbc0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cakephp/validation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fig/http-message-util' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '9d94dc0154230ac39e5bf89398b324a86f63f765',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fig/http-message-util',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.11.1',
            'version' => '6.11.1.0',
            'reference' => 'd1e91ecf8c598d073d0995afa8cd5c75c6e19e66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'b352cf0534aa1ae6b4d825d1e762e35d43f8a841',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/container' => array(
            'pretty_version' => '4.2.5',
            'version' => '4.2.5.0',
            'reference' => 'd3cebb0ff4685ff61c749e54b27db49319e2ec00',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.9.0',
            'version' => '3.9.0.0',
            'reference' => '10d85740180ecba7896c87e06a166e0c95a0e3b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/fast-route' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '181d480e08d9476e61381e04a71b34dc0432e812',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/fast-route',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'orno/di' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '~2.0',
            ),
        ),
        'php-di/invoker' => array(
            'pretty_version' => '2.3.6',
            'version' => '2.3.6.0',
            'reference' => '59f15608528d8a8838d69b422a919fd6b16aa576',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-di/invoker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-di/php-di' => array(
            'pretty_version' => '7.0.11',
            'version' => '7.0.11.0',
            'reference' => '32f111a6d214564520a57831d397263e8946c1d2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-di/php-di',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '1.9.3.0',
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.0',
                1 => '^2.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.0 || ^2.0',
            ),
        ),
        'psr/http-server-handler' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '84c4fb66179be4caaf8e97bd239203245302e7d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-server-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-server-middleware' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'c1481f747daaa6a0782775cd6a8c26a1bf4a3829',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-server-middleware',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '344572933ad0181accbf4ba763e85a0306a8c5e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.9.0',
            'version' => '4.9.0.0',
            'reference' => '4e0e23cc785f0724a0e838279a9eb03f28b092a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'responsive-sk/hdm-boot' => array(
            'pretty_version' => '2.1.2',
            'version' => '2.1.2.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'responsive-sk/slim4-paths' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '67b2085e0c8d19228ce63824bf09ec678ee4e65f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../responsive-sk/slim4-paths',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'responsive-sk/slim4-session' => array(
            'pretty_version' => '2.2.2',
            'version' => '2.2.2.0',
            'reference' => 'a302798ce94d3f5c81448da61db9ec68f9fc0650',
            'type' => 'library',
            'install_path' => __DIR__ . '/../responsive-sk/slim4-session',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.9.0',
            ),
        ),
        'slim/php-view' => array(
            'pretty_version' => '3.4.0',
            'version' => '3.4.0.0',
            'reference' => 'ef1821663a6a028b9e446e8c6818fd257bf70313',
            'type' => 'library',
            'install_path' => __DIR__ . '/../slim/php-view',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'slim/psr7' => array(
            'pretty_version' => '1.7.1',
            'version' => '1.7.1.0',
            'reference' => 'fe98653e7983010aa85c1d137c9b9ad5a1cd187d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../slim/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'slim/slim' => array(
            'pretty_version' => '4.14.0',
            'version' => '4.14.0.0',
            'reference' => '5943393b88716eb9e82c4161caa956af63423913',
            'type' => 'library',
            'install_path' => __DIR__ . '/../slim/slim',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.2',
            'version' => '5.6.2.0',
            'reference' => '24ac4c74f91ee2c193fa1aaa5c249cb0822809af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
