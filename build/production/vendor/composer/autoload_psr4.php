<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Slim\\Views\\' => array($vendorDir . '/slim/php-view/src'),
    'Slim\\Psr7\\' => array($vendorDir . '/slim/psr7/src'),
    'Slim\\' => array($vendorDir . '/slim/slim/Slim'),
    'ResponsiveSk\\Slim4Session\\' => array($vendorDir . '/responsive-sk/slim4-session/src'),
    'ResponsiveSk\\Slim4Paths\\' => array($vendorDir . '/responsive-sk/slim4-paths/src'),
    'Ramsey\\Uuid\\' => array($vendorDir . '/ramsey/uuid/src'),
    'Ramsey\\Collection\\' => array($vendorDir . '/ramsey/collection/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Http\\Server\\' => array($vendorDir . '/psr/http-server-middleware/src', $vendorDir . '/psr/http-server-handler/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\EventDispatcher\\' => array($vendorDir . '/psr/event-dispatcher/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'PhpOption\\' => array($vendorDir . '/phpoption/phpoption/src/PhpOption'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'League\\Container\\' => array($vendorDir . '/league/container/src'),
    'Laravel\\SerializableClosure\\' => array($vendorDir . '/laravel/serializable-closure/src'),
    'Invoker\\' => array($vendorDir . '/php-di/invoker/src'),
    'HdmBoot\\Modules\\Core\\' => array($baseDir . '/src/Modules/Core'),
    'HdmBoot\\' => array($baseDir . '/src'),
    'GrahamCampbell\\ResultType\\' => array($vendorDir . '/graham-campbell/result-type/src'),
    'Firebase\\JWT\\' => array($vendorDir . '/firebase/php-jwt/src'),
    'Fig\\Http\\Message\\' => array($vendorDir . '/fig/http-message-util/src'),
    'FastRoute\\' => array($vendorDir . '/nikic/fast-route/src'),
    'Dotenv\\' => array($vendorDir . '/vlucas/phpdotenv/src'),
    'DI\\' => array($vendorDir . '/php-di/php-di/src'),
    'Cake\\Validation\\' => array($vendorDir . '/cakephp/validation'),
    'Cake\\Utility\\' => array($vendorDir . '/cakephp/utility'),
    'Cake\\Core\\' => array($vendorDir . '/cakephp/core'),
    'Brick\\Math\\' => array($vendorDir . '/brick/math/src'),
);
