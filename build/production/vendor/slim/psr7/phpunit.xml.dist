<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.5/phpunit.xsd"
         backupGlobals="true"
         beStrictAboutOutputDuringTests="true"
         colors="true"
         bootstrap="tests/bootstrap.php"
>
    <testsuites>
        <testsuite name="Slim-Psr7 Test Suite">
            <directory>tests</directory>
        </testsuite>
    </testsuites>

    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">src</directory>
        </include>
        <report>
            <html outputDirectory="coverage" lowUpperBound="20" highLowerBound="50"/>
        </report>
    </coverage>
</phpunit>
