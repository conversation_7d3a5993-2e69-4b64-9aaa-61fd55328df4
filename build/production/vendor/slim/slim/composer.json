{"name": "slim/slim", "type": "library", "description": "Slim is a PHP micro framework that helps you quickly write simple yet powerful web applications and APIs", "keywords": ["framework", "micro", "api", "router"], "homepage": "https://www.slimframework.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://joshlockhart.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://silentworks.co.uk"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://akrabat.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.lgse.com"}, {"name": "<PERSON>", "email": "g<PERSON><PERSON>@me.com", "homepage": "http://gabrielmanricks.com"}], "support": {"docs": "https://www.slimframework.com/docs/v4/", "forum": "https://discourse.slimframework.com/", "irc": "irc://irc.freenode.net:6667/slimphp", "issues": "https://github.com/slimphp/Slim/issues", "rss": "https://www.slimframework.com/blog/feed.rss", "slack": "https://slimphp.slack.com/", "source": "https://github.com/slimphp/Slim", "wiki": "https://github.com/slimphp/Slim/wiki"}, "require": {"php": "^7.4 || ^8.0", "ext-json": "*", "nikic/fast-route": "^1.3", "psr/container": "^1.0 || ^2.0", "psr/http-factory": "^1.1", "psr/http-message": "^1.1 || ^2.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "require-dev": {"ext-simplexml": "*", "adriansuter/php-autoload-override": "^1.4", "guzzlehttp/psr7": "^2.6", "httpsoft/http-message": "^1.1", "httpsoft/http-server-request": "^1.1", "laminas/laminas-diactoros": "^2.17 || ^3", "nyholm/psr7": "^1.8", "nyholm/psr7-server": "^1.1", "phpspec/prophecy": "^1.19", "phpspec/prophecy-phpunit": "^2.1", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^9.6", "slim/http": "^1.3", "slim/psr7": "^1.6", "squizlabs/php_codesniffer": "^3.10", "vimeo/psalm": "^5.24"}, "autoload": {"psr-4": {"Slim\\": "<PERSON>"}}, "autoload-dev": {"psr-4": {"Slim\\Tests\\": "tests"}}, "scripts": {"test": ["@phpunit", "@phpcs", "@phpstan", "@psalm"], "phpunit": "phpunit", "phpcs": "phpcs", "phpstan": "phpstan --memory-limit=-1", "psalm": "psalm --no-cache"}, "suggest": {"ext-simplexml": "Needed to support XML format in BodyParsingMiddleware", "ext-xml": "Needed to support XML format in BodyParsingMiddleware", "slim/psr7": "Slim PSR-7 implementation. See https://www.slimframework.com/docs/v4/start/installation.html for more information.", "php-di/php-di": "PHP-DI is the recommended container library to be used with Slim"}, "config": {"sort-packages": true}}