{"name": "cakephp/utility", "description": "CakePHP Utility classes such as Inflector, String, Hash, and Security", "type": "library", "keywords": ["cakephp", "utility", "inflector", "string", "hash", "security"], "homepage": "https://cakephp.org", "license": "MIT", "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/utility/graphs/contributors"}], "support": {"issues": "https://github.com/cakephp/cakephp/issues", "forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "source": "https://github.com/cakephp/utility"}, "require": {"php": ">=8.1", "cakephp/core": "5.2.*@dev"}, "autoload": {"psr-4": {"Cake\\Utility\\": "."}, "files": ["bootstrap.php"]}, "suggest": {"ext-intl": "To use Text::transliterate() or Text::slug()", "lib-ICU": "To use Text::transliterate() or Text::slug()"}, "prefer-stable": true, "extra": {"branch-alias": {"dev-5.x": "5.2.x-dev"}}}