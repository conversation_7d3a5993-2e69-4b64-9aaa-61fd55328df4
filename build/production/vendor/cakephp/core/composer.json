{"name": "cakephp/core", "description": "CakePHP Framework Core classes", "type": "library", "keywords": ["cakephp", "framework", "core"], "homepage": "https://cakephp.org", "license": "MIT", "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/core/graphs/contributors"}], "support": {"issues": "https://github.com/cakephp/cakephp/issues", "forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "source": "https://github.com/cakephp/core"}, "require": {"php": ">=8.1", "cakephp/utility": "5.2.*@dev", "league/container": "^4.2", "psr/container": "^1.1 || ^2.0"}, "autoload": {"psr-4": {"Cake\\Core\\": "."}, "files": ["functions.php"]}, "provide": {"psr/container-implementation": "^2.0"}, "suggest": {"cakephp/event": "To use PluginApplicationInterface or plugin applications.", "cakephp/cache": "To use Configure::store() and restore().", "league/container": "To use Container and ServiceProvider classes"}, "prefer-stable": true, "extra": {"branch-alias": {"dev-5.x": "5.2.x-dev"}}}