{"name": "cakephp/validation", "description": "CakePHP Validation library", "type": "library", "keywords": ["cakephp", "validation", "data validation"], "homepage": "https://cakephp.org", "license": "MIT", "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/validation/graphs/contributors"}], "support": {"issues": "https://github.com/cakephp/cakephp/issues", "forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "source": "https://github.com/cakephp/validation"}, "require": {"php": ">=8.1", "cakephp/core": "5.2.*@dev", "cakephp/utility": "5.2.*@dev", "psr/http-message": "^1.1 || ^2.0"}, "require-dev": {"cakephp/i18n": "5.2.*@dev"}, "autoload": {"psr-4": {"Cake\\Validation\\": "."}}, "suggest": {"cakephp/i18n": "If you want to use Validation::localizedTime()"}, "prefer-stable": true, "extra": {"branch-alias": {"dev-5.x": "5.2.x-dev"}}}