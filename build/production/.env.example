# HDM Boot Production Configuration
APP_ENV=production
APP_DEBUG=false
APP_NAME="HDM Boot Application"

# Database Configuration
DATABASE_PATH=var/storage/
MARK_DATABASE_PATH=var/storage/mark.db
USER_DATABASE_PATH=var/storage/user.db
SYSTEM_DATABASE_PATH=var/storage/system.db

# Permission Configuration
PERMISSIONS_STRICT=false

# Security Configuration
SESSION_SECURE=true
SESSION_HTTPONLY=true
SESSION_SAMESITE=Strict
HTTPS=true

# Cache Configuration
CACHE_ENABLED=true
TEMPLATE_CACHE_ENABLED=true

# Logging Configuration
LOG_LEVEL=error
LOG_PATH=var/logs/

# Replace these with your actual values
SECRET_KEY=CHANGE_THIS_SECRET_KEY_IN_PRODUCTION
CSRF_SECRET=CHANGE_THIS_CSRF_SECRET_IN_PRODUCTION