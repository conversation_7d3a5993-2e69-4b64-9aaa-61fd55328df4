[2025-06-28 14:10:59] app.INFO: Starting module discovery {"modules_path":"/home/<USER>/Desktop/06/hdm-boot/build/production/src/Modules"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module registered successfully {"module_name":"Storage","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: <PERSON>dule registered successfully {"module_name":"Template","dependencies":["Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module registered successfully {"module_name":"Session","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module registered successfully {"module_name":"Testing","dependencies":["Logging","Database","Storage"]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module registered successfully {"module_name":"User","dependencies":["Database"]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module registered successfully {"module_name":"Mark","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module registered successfully {"module_name":"Security","dependencies":["User","Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module registered successfully {"module_name":"Logging","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module registered successfully {"module_name":"Language","dependencies":["User"]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module registered successfully {"module_name":"Database","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module registered successfully {"module_name":"Blog","dependencies":["Storage","Template"]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module discovery completed {"discovered_modules":11,"module_names":["Storage","Template","Session","Testing","User","Mark","Security","Logging","Language","Database","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Starting module initialization  {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialized successfully {"module_name":"Storage"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialized successfully {"module_name":"Session"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialized successfully {"module_name":"Template"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialized successfully {"module_name":"Logging"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialized successfully {"module_name":"Database"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialized successfully {"module_name":"Testing"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialized successfully {"module_name":"User"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialized successfully {"module_name":"Mark"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialized successfully {"module_name":"Security"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialized successfully {"module_name":"Language"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialized successfully {"module_name":"Blog"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module initialization completed {"initialized_modules":["Storage","Session","Template","Logging","Database","Testing","User","Mark","Security","Language","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Loading module services  {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module services loaded {"total_services":52,"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"59bdcc8"}
[2025-06-28 14:10:59] app.INFO: Module routes loaded {"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"24e456b"}
[2025-06-28 14:10:59] app.INFO: Created translations directory {"path":"/home/<USER>/Desktop/06/hdm-boot/build/production/resources/translations"} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"24e456b"}
[2025-06-28 14:10:59] app.INFO: Starting monitoring bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"24e456b"}
[2025-06-28 14:10:59] app.INFO: Health checks registered {"registered_checks":["database","filesystem","application"]} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"24e456b"}
[2025-06-28 14:10:59] app.INFO: Monitoring bootstrap completed  {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"24e456b"}
[2025-06-28 14:10:59] app.INFO: Starting event system bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"24e456b"}
[2025-06-28 14:10:59] app.INFO: Event system bootstrap completed {"registered_listeners":3,"module_subscriptions":2} {"process_id":27292,"memory_usage":"2 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"24e456b"}
[2025-06-28 14:10:59] app.INFO: Language set successfully {"locale":"en_US","domain":"messages","setlocale_result":false} {"process_id":27292,"memory_usage":"4 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"24e456b"}
[2025-06-28 14:10:59] app.WARNING: Failed to set locale via middleware {"locale":"en_US"} {"process_id":27292,"memory_usage":"4 MB","url":"/mark","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark/dashboard","uid":"24e456b"}
[2025-06-28 14:11:06] app.INFO: Starting module discovery {"modules_path":"/home/<USER>/Desktop/06/hdm-boot/build/production/src/Modules"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module registered successfully {"module_name":"Storage","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module registered successfully {"module_name":"Template","dependencies":["Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module registered successfully {"module_name":"Session","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module registered successfully {"module_name":"Testing","dependencies":["Logging","Database","Storage"]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module registered successfully {"module_name":"User","dependencies":["Database"]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module registered successfully {"module_name":"Mark","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module registered successfully {"module_name":"Security","dependencies":["User","Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module registered successfully {"module_name":"Logging","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module registered successfully {"module_name":"Language","dependencies":["User"]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module registered successfully {"module_name":"Database","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module registered successfully {"module_name":"Blog","dependencies":["Storage","Template"]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module discovery completed {"discovered_modules":11,"module_names":["Storage","Template","Session","Testing","User","Mark","Security","Logging","Language","Database","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Starting module initialization  {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialized successfully {"module_name":"Storage"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialized successfully {"module_name":"Session"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialized successfully {"module_name":"Template"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialized successfully {"module_name":"Logging"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialized successfully {"module_name":"Database"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialized successfully {"module_name":"Testing"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialized successfully {"module_name":"User"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialized successfully {"module_name":"Mark"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialized successfully {"module_name":"Security"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialized successfully {"module_name":"Language"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialized successfully {"module_name":"Blog"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module initialization completed {"initialized_modules":["Storage","Session","Template","Logging","Database","Testing","User","Mark","Security","Language","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Loading module services  {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module services loaded {"total_services":52,"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"b68cce6"}
[2025-06-28 14:11:06] app.INFO: Module routes loaded {"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"ec4c0fc"}
[2025-06-28 14:11:06] app.INFO: Starting monitoring bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"ec4c0fc"}
[2025-06-28 14:11:06] app.INFO: Health checks registered {"registered_checks":["database","filesystem","application"]} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"ec4c0fc"}
[2025-06-28 14:11:06] app.INFO: Monitoring bootstrap completed  {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"ec4c0fc"}
[2025-06-28 14:11:06] app.INFO: Starting event system bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"ec4c0fc"}
[2025-06-28 14:11:06] app.INFO: Event system bootstrap completed {"registered_listeners":3,"module_subscriptions":2} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"ec4c0fc"}
[2025-06-28 14:11:06] app.INFO: Language set successfully {"locale":"en_US","domain":"messages","setlocale_result":false} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"ec4c0fc"}
[2025-06-28 14:11:06] app.WARNING: Failed to set locale via middleware {"locale":"en_US"} {"process_id":27292,"memory_usage":"2 MB","url":"/login","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/mark","uid":"ec4c0fc"}
[2025-06-28 14:11:07] app.INFO: Starting module discovery {"modules_path":"/home/<USER>/Desktop/06/hdm-boot/build/production/src/Modules"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module registered successfully {"module_name":"Storage","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module registered successfully {"module_name":"Template","dependencies":["Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module registered successfully {"module_name":"Session","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module registered successfully {"module_name":"Testing","dependencies":["Logging","Database","Storage"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module registered successfully {"module_name":"User","dependencies":["Database"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module registered successfully {"module_name":"Mark","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module registered successfully {"module_name":"Security","dependencies":["User","Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module registered successfully {"module_name":"Logging","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module registered successfully {"module_name":"Language","dependencies":["User"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module registered successfully {"module_name":"Database","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module registered successfully {"module_name":"Blog","dependencies":["Storage","Template"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module discovery completed {"discovered_modules":11,"module_names":["Storage","Template","Session","Testing","User","Mark","Security","Logging","Language","Database","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Starting module initialization  {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialized successfully {"module_name":"Storage"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialized successfully {"module_name":"Session"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialized successfully {"module_name":"Template"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialized successfully {"module_name":"Logging"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialized successfully {"module_name":"Database"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialized successfully {"module_name":"Testing"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialized successfully {"module_name":"User"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialized successfully {"module_name":"Mark"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialized successfully {"module_name":"Security"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialized successfully {"module_name":"Language"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialized successfully {"module_name":"Blog"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module initialization completed {"initialized_modules":["Storage","Session","Template","Logging","Database","Testing","User","Mark","Security","Language","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Loading module services  {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module services loaded {"total_services":52,"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"b6584c6"}
[2025-06-28 14:11:07] app.INFO: Module routes loaded {"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"c8a8948"}
[2025-06-28 14:11:07] app.INFO: Starting monitoring bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"c8a8948"}
[2025-06-28 14:11:07] app.INFO: Health checks registered {"registered_checks":["database","filesystem","application"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"c8a8948"}
[2025-06-28 14:11:07] app.INFO: Monitoring bootstrap completed  {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"c8a8948"}
[2025-06-28 14:11:07] app.INFO: Starting event system bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"c8a8948"}
[2025-06-28 14:11:07] app.INFO: Event system bootstrap completed {"registered_listeners":3,"module_subscriptions":2} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"c8a8948"}
[2025-06-28 14:11:07] app.INFO: Language set successfully {"locale":"en_US","domain":"messages","setlocale_result":false} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"c8a8948"}
[2025-06-28 14:11:07] app.WARNING: Failed to set locale via middleware {"locale":"en_US"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"c8a8948"}
[2025-06-28 14:11:09] app.INFO: Starting module discovery {"modules_path":"/home/<USER>/Desktop/06/hdm-boot/build/production/src/Modules"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module registered successfully {"module_name":"Storage","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module registered successfully {"module_name":"Template","dependencies":["Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module registered successfully {"module_name":"Session","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module registered successfully {"module_name":"Testing","dependencies":["Logging","Database","Storage"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module registered successfully {"module_name":"User","dependencies":["Database"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module registered successfully {"module_name":"Mark","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module registered successfully {"module_name":"Security","dependencies":["User","Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module registered successfully {"module_name":"Logging","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module registered successfully {"module_name":"Language","dependencies":["User"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module registered successfully {"module_name":"Database","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module registered successfully {"module_name":"Blog","dependencies":["Storage","Template"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module discovery completed {"discovered_modules":11,"module_names":["Storage","Template","Session","Testing","User","Mark","Security","Logging","Language","Database","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Starting module initialization  {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialized successfully {"module_name":"Storage"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialized successfully {"module_name":"Session"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialized successfully {"module_name":"Template"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialized successfully {"module_name":"Logging"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialized successfully {"module_name":"Database"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialized successfully {"module_name":"Testing"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialized successfully {"module_name":"User"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialized successfully {"module_name":"Mark"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialized successfully {"module_name":"Security"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialized successfully {"module_name":"Language"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialized successfully {"module_name":"Blog"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module initialization completed {"initialized_modules":["Storage","Session","Template","Logging","Database","Testing","User","Mark","Security","Language","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Loading module services  {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module services loaded {"total_services":52,"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"975e4cb"}
[2025-06-28 14:11:09] app.INFO: Module routes loaded {"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"e43e798"}
[2025-06-28 14:11:09] app.INFO: Starting monitoring bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"e43e798"}
[2025-06-28 14:11:09] app.INFO: Health checks registered {"registered_checks":["database","filesystem","application"]} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"e43e798"}
[2025-06-28 14:11:09] app.INFO: Monitoring bootstrap completed  {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"e43e798"}
[2025-06-28 14:11:09] app.INFO: Starting event system bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"e43e798"}
[2025-06-28 14:11:09] app.INFO: Event system bootstrap completed {"registered_listeners":3,"module_subscriptions":2} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"e43e798"}
[2025-06-28 14:11:09] app.INFO: Language set successfully {"locale":"en_US","domain":"messages","setlocale_result":false} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"e43e798"}
[2025-06-28 14:11:09] app.WARNING: Failed to set locale via middleware {"locale":"en_US"} {"process_id":27292,"memory_usage":"2 MB","url":"/","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/login","uid":"e43e798"}
[2025-06-28 14:11:12] app.INFO: Starting module discovery {"modules_path":"/home/<USER>/Desktop/06/hdm-boot/build/production/src/Modules"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module registered successfully {"module_name":"Storage","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module registered successfully {"module_name":"Template","dependencies":["Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module registered successfully {"module_name":"Session","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module registered successfully {"module_name":"Testing","dependencies":["Logging","Database","Storage"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module registered successfully {"module_name":"User","dependencies":["Database"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module registered successfully {"module_name":"Mark","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module registered successfully {"module_name":"Security","dependencies":["User","Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module registered successfully {"module_name":"Logging","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module registered successfully {"module_name":"Language","dependencies":["User"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module registered successfully {"module_name":"Database","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module registered successfully {"module_name":"Blog","dependencies":["Storage","Template"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module discovery completed {"discovered_modules":11,"module_names":["Storage","Template","Session","Testing","User","Mark","Security","Logging","Language","Database","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Starting module initialization  {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialized successfully {"module_name":"Storage"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialized successfully {"module_name":"Session"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialized successfully {"module_name":"Template"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialized successfully {"module_name":"Logging"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialized successfully {"module_name":"Database"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialized successfully {"module_name":"Testing"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialized successfully {"module_name":"User"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialized successfully {"module_name":"Mark"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialized successfully {"module_name":"Security"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialized successfully {"module_name":"Language"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialized successfully {"module_name":"Blog"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module initialization completed {"initialized_modules":["Storage","Session","Template","Logging","Database","Testing","User","Mark","Security","Language","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Loading module services  {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module services loaded {"total_services":52,"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"afbe745"}
[2025-06-28 14:11:12] app.INFO: Module routes loaded {"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"3fded29"}
[2025-06-28 14:11:12] app.INFO: Starting monitoring bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"3fded29"}
[2025-06-28 14:11:12] app.INFO: Health checks registered {"registered_checks":["database","filesystem","application"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"3fded29"}
[2025-06-28 14:11:12] app.INFO: Monitoring bootstrap completed  {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"3fded29"}
[2025-06-28 14:11:12] app.INFO: Starting event system bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"3fded29"}
[2025-06-28 14:11:12] app.INFO: Event system bootstrap completed {"registered_listeners":3,"module_subscriptions":2} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"3fded29"}
[2025-06-28 14:11:12] app.INFO: Language set successfully {"locale":"en_US","domain":"messages","setlocale_result":false} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"3fded29"}
[2025-06-28 14:11:12] app.WARNING: Failed to set locale via middleware {"locale":"en_US"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/","uid":"3fded29"}
[2025-06-28 14:11:25] app.INFO: Starting module discovery {"modules_path":"/home/<USER>/Desktop/06/hdm-boot/build/production/src/Modules"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module registered successfully {"module_name":"Storage","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module registered successfully {"module_name":"Template","dependencies":["Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module registered successfully {"module_name":"Session","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module registered successfully {"module_name":"Testing","dependencies":["Logging","Database","Storage"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module registered successfully {"module_name":"User","dependencies":["Database"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module registered successfully {"module_name":"Mark","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module registered successfully {"module_name":"Security","dependencies":["User","Session"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module registered successfully {"module_name":"Logging","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module registered successfully {"module_name":"Language","dependencies":["User"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module registered successfully {"module_name":"Database","dependencies":[]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module registered successfully {"module_name":"Blog","dependencies":["Storage","Template"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module discovery completed {"discovered_modules":11,"module_names":["Storage","Template","Session","Testing","User","Mark","Security","Logging","Language","Database","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Starting module initialization  {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialized successfully {"module_name":"Storage"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialized successfully {"module_name":"Session"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialized successfully {"module_name":"Template"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialized successfully {"module_name":"Logging"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialized successfully {"module_name":"Database"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialized successfully {"module_name":"Testing"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialized successfully {"module_name":"User"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialized successfully {"module_name":"Mark"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialized successfully {"module_name":"Security"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialized successfully {"module_name":"Language"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialized successfully {"module_name":"Blog"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module initialization completed {"initialized_modules":["Storage","Session","Template","Logging","Database","Testing","User","Mark","Security","Language","Blog"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Loading module services  {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module services loaded {"total_services":52,"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e33127c"}
[2025-06-28 14:11:25] app.INFO: Module routes loaded {"modules_count":11} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e0449e1"}
[2025-06-28 14:11:25] app.INFO: Starting monitoring bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e0449e1"}
[2025-06-28 14:11:25] app.INFO: Health checks registered {"registered_checks":["database","filesystem","application"]} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e0449e1"}
[2025-06-28 14:11:25] app.INFO: Monitoring bootstrap completed  {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e0449e1"}
[2025-06-28 14:11:25] app.INFO: Starting event system bootstrap  {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e0449e1"}
[2025-06-28 14:11:25] app.INFO: Event system bootstrap completed {"registered_listeners":3,"module_subscriptions":2} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e0449e1"}
[2025-06-28 14:11:25] app.INFO: Language set successfully {"locale":"en_US","domain":"messages","setlocale_result":false} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e0449e1"}
[2025-06-28 14:11:25] app.WARNING: Failed to set locale via middleware {"locale":"en_US"} {"process_id":27292,"memory_usage":"2 MB","url":"/blog","ip":"::1","http_method":"GET","server":"localhost","referrer":"http://localhost:8080/blog","uid":"e0449e1"}
