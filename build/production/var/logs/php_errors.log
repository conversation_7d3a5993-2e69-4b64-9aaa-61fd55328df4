[28-Jun-2025 14:37:57 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:38:01 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:38:03 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:38:08 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 LOGIN DEBUG: Starting login for email: <EMAIL>
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 LOGIN DEBUG: Client IP: ::1
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 LOGIN DEBUG: About to authenticate user: <EMAIL>
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 AUTH DEBUG: Starting security check for: <EMAIL> from IP: ::1
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 AUTH DEBUG: Security check passed for: <EMAIL>
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 AUTH DEBUG: Calling userService->authenticate for: <EMAIL>
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 USER DEBUG: Looking up user by email: <EMAIL>
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 USER REPO DEBUG: Looking for user: <EMAIL>
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 USER REPO DEBUG: PDO connection: OK
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 USER REPO DEBUG: Query result: FOUND
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 USER REPO DEBUG: User data: {"id":"user-685ff11203fcd","email":"<EMAIL>","name":"Example User","password_hash":"$2y$12$vrVJ9Jo5wmNRvuD\/SVS0re1ZIRBbx44WOyI39RwSbJvFxj29jdDw2","role":"user","status":"active","email_verified":1,"last_login_at":null,"login_count":0,"created_at":"2025-06-28 13:41:38","updated_at":"2025-06-28 13:41:38"}
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 USER DEBUG: User found, checking password for: <EMAIL>
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 PASSWORD DEBUG: Checking password for: <EMAIL>
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 PASSWORD DEBUG: Input password: user123
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 PASSWORD DEBUG: Stored hash: $2y$12$vrVJ9Jo5wmNRvuD/SVS0re1ZIRBbx44WOyI39RwSbJvFxj29jdDw2
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 PASSWORD DEBUG: Password verification SUCCESS for: <EMAIL>
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 AUTH DEBUG: UserService result: USER_FOUND
[28-Jun-2025 16:38:08 Europe/Bratislava] 🔍 LOGIN DEBUG: Authentication result: SUCCESS
[28-Jun-2025 14:38:08 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:38:13 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:38:13 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:38:16 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:38:16 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:38:26 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:38:32 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:38:32 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:40:42 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:40:42 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:40:46 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:40:47 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:40:50 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
[28-Jun-2025 14:40:54 UTC] Loaded modules: Storage, Template, Session, Testing, User, Mark, Security, Logging, Language, Database, Blog
