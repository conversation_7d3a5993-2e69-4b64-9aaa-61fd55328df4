⚡ [2025-06-28 16:10:59] performance.INFO: <PERSON>ric recorded | Context: {"metric_name":"memory.usage.bootstrap","metric_value":2097152,"timestamp":1751119859.040346} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:10:59] performance.INFO: Metric recorded | Context: {"metric_name":"memory.peak.bootstrap","metric_value":2097152,"timestamp":1751119859.040483} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:10:59] performance.INFO: Memory usage recorded | Context: {"context":"bootstrap","memory_usage":2097152,"memory_peak":2097152,"memory_usage_mb":2.0,"memory_peak_mb":2.0} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:06] performance.INFO: <PERSON><PERSON> recorded | Context: {"metric_name":"memory.usage.bootstrap","metric_value":2097152,"timestamp":1751119866.428778} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:06] performance.INFO: Metric recorded | Context: {"metric_name":"memory.peak.bootstrap","metric_value":2097152,"timestamp":1751119866.42886} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:06] performance.INFO: Memory usage recorded | Context: {"context":"bootstrap","memory_usage":2097152,"memory_peak":2097152,"memory_usage_mb":2.0,"memory_peak_mb":2.0} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:07] performance.INFO: Metric recorded | Context: {"metric_name":"memory.usage.bootstrap","metric_value":2097152,"timestamp":1751119867.953974} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:07] performance.INFO: Metric recorded | Context: {"metric_name":"memory.peak.bootstrap","metric_value":2097152,"timestamp":1751119867.954042} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:07] performance.INFO: Memory usage recorded | Context: {"context":"bootstrap","memory_usage":2097152,"memory_peak":2097152,"memory_usage_mb":2.0,"memory_peak_mb":2.0} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:09] performance.INFO: Metric recorded | Context: {"metric_name":"memory.usage.bootstrap","metric_value":2097152,"timestamp":1751119869.716223} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:09] performance.INFO: Metric recorded | Context: {"metric_name":"memory.peak.bootstrap","metric_value":2097152,"timestamp":1751119869.716292} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:09] performance.INFO: Memory usage recorded | Context: {"context":"bootstrap","memory_usage":2097152,"memory_peak":2097152,"memory_usage_mb":2.0,"memory_peak_mb":2.0} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:12] performance.INFO: Metric recorded | Context: {"metric_name":"memory.usage.bootstrap","metric_value":2097152,"timestamp":1751119872.06787} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:12] performance.INFO: Metric recorded | Context: {"metric_name":"memory.peak.bootstrap","metric_value":2097152,"timestamp":1751119872.067946} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:12] performance.INFO: Memory usage recorded | Context: {"context":"bootstrap","memory_usage":2097152,"memory_peak":2097152,"memory_usage_mb":2.0,"memory_peak_mb":2.0} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:25] performance.INFO: Metric recorded | Context: {"metric_name":"memory.usage.bootstrap","metric_value":2097152,"timestamp":1751119885.027325} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:25] performance.INFO: Metric recorded | Context: {"metric_name":"memory.peak.bootstrap","metric_value":2097152,"timestamp":1751119885.027395} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
⚡ [2025-06-28 16:11:25] performance.INFO: Memory usage recorded | Context: {"context":"bootstrap","memory_usage":2097152,"memory_peak":2097152,"memory_usage_mb":2.0,"memory_peak_mb":2.0} | Extra: {"performance_metric":true,"memory_usage":"2 MB"}
