<?php

declare(strict_types=1);

namespace DI\Definition\Source;

use DI\Definition\Exception\InvalidDefinition;
use DI\Definition\ObjectDefinition;

/**
 * Source of definitions for entries of the container.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface Autowiring
{
    /**
     * Autowire the given definition.
     *
     * @throws InvalidDefinition An invalid definition was found.
     */
    public function autowire(string $name, ?ObjectDefinition $definition = null) : ?ObjectDefinition;
}
