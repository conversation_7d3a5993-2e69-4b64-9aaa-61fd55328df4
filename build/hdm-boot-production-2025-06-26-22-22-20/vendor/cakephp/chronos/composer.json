{"name": "cakephp/chronos", "description": "A simple API extension for DateTime.", "license": "MIT", "type": "library", "keywords": ["date", "time", "DateTime"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "The CakePHP Team", "homepage": "https://cakephp.org"}], "homepage": "https://cakephp.org", "support": {"issues": "https://github.com/cakephp/chronos/issues", "source": "https://github.com/cakephp/chronos"}, "require": {"php": ">=8.1", "psr/clock": "^1.0"}, "require-dev": {"cakephp/cakephp-codesniffer": "^5.0", "phpunit/phpunit": "^10.1.0 || ^11.1.3"}, "provide": {"psr/clock-implementation": "1.0"}, "autoload": {"psr-4": {"Cake\\Chronos\\": "src/"}}, "autoload-dev": {"psr-4": {"Cake\\Chronos\\Test\\": "tests/"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "scripts": {"check": ["@test", "@cs-check", "@stan"], "cs-check": "phpcs --colors --parallel=16 -p", "cs-fix": "phpcbf --colors --parallel=16 -p", "phpstan": "tools/phpstan analyse", "psalm": "tools/psalm --show-info=false", "psalm-baseline": "tools/psalm  --set-baseline=psalm-baseline.xml", "stan": ["@phpstan", "@psalm"], "stan-baseline": "tools/phpstan --generate-baseline", "stan-setup": "phive install", "test": "phpunit"}}