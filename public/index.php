<?php

declare(strict_types=1);

// Error reporting for debugging on shared hosting
error_reporting(E_ALL);
ini_set('display_errors', '1');
ini_set('log_errors', '1');
ini_set('error_log', __DIR__ . '/../var/logs/php_errors.log');

// Memory and time limits for shared hosting
ini_set('memory_limit', '256M');
ini_set('max_execution_time', '60');

try {
    use HdmBoot\Boot\App;

    require_once __DIR__ . '/../vendor/autoload.php';

    (new App())->run();
} catch (Throwable $e) {
    // Log the error
    error_log("Fatal error in index.php: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
    error_log("Stack trace: " . $e->getTraceAsString());

    // Show user-friendly error
    http_response_code(500);
    echo "<!DOCTYPE html><html><head><title>Application Error</title></head><body>";
    echo "<h1>Application Error</h1>";
    echo "<p>The application encountered an error. Please check the logs.</p>";
    if (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] === 'true') {
        echo "<pre>" . htmlspecialchars($e->getMessage()) . "</pre>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
    echo "</body></html>";
}
