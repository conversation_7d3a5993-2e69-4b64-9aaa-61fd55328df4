# HDM Boot Public Directory

# PHP Configuration for Shared Hosting
php_value memory_limit 256M
php_value max_execution_time 60
php_value max_input_time 60
php_value post_max_size 32M
php_value upload_max_filesize 32M

# Error Handling
php_flag display_errors Off
php_flag log_errors On

RewriteEngine On

# Handle all requests through index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>