# HDM Boot Framework Configuration
# This file is an example of the .env file used for configuration.
# Copy this file to .env and modify it as needed.
#
# IMPORTANT: Generate secure keys using: php bin/generate-keys.php

# Application Configuration
APP_NAME="HDM Boot"
APP_ENV=dev
APP_DEBUG=true
APP_TIMEZONE=UTC

# Database (path is relative to application root, not public/) Configuration
DATABASE_URL="sqlite:var/storage/app.db"

# Security Configuration (REQUIRED)
# Generate secure keys: php bin/generate-keys.php
JWT_SECRET="CHANGE_ME_GENERATE_WITH_php_bin_generate-keys_php"
JWT_EXPIRY=3600

# Module Configuration
# Comma-separated list of optional modules to enable
ENABLED_MODULES="Blog"

# Session Configuration
SESSION_NAME="hdm_boot_session"
SESSION_LIFETIME=7200
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax

# Language/Localization Configuration
DEFAULT_LOCALE=en_US
DEFAULT_TIMEZONE=Europe/Bratislava
ENABLE_SLOVAK=true
ENABLE_CZECH=true
ENABLE_GERMAN=false
ENABLE_FRENCH=false
ENABLE_SPANISH=false
ENABLE_ITALIAN=false
ENABLE_POLISH=false

# Translation Settings
TRANSLATIONS_PATH=resources/translations
TRANSLATION_DOMAIN=messages
TRANSLATION_FALLBACK=true
GETTEXT_ENABLED=true

# Language Detection
AUTO_DETECT_LANGUAGE=true
USE_BROWSER_LANGUAGE=true
USE_USER_PREFERENCE=true
LANGUAGE_COOKIE_NAME=app_language

# Language API
LANGUAGE_API_ENABLED=true
LANGUAGE_API_REQUIRE_AUTH=false

# Development
LANGUAGE_DEBUG=false
LOG_MISSING_TRANSLATIONS=true
